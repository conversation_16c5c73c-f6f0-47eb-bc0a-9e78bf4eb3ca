import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { 
  LayoutDashboard, 
  Users, 
  UserPlus,
  FileText,
  Settings,
  LogOut,
  ChevronDown,
  Package,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import logoHeroDark from '@/assets/logo/logo hero-dark.png';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface NavItem {
  title: string;
  href?: string;
  icon: React.ReactNode;
  children?: NavItem[];
  badge?: string;
  onClick?: () => void;
}

export function DashboardSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, funcionario, signOut } = useAuth();
  const [openItems, setOpenItems] = React.useState<string[]>([]);

  const isAdmin = funcionario?.nivel_acesso === 'admin' || funcionario?.nivel_acesso === 'OWNER';
  const isFuncionario = funcionario?.nivel_acesso === 'funcionario' || funcionario?.nivel_acesso === 'FUNCIONARIO';

  const adminNavItems: NavItem[] = [
    {
      title: 'Visão Geral',
      onClick: () => navigate('/admin'),
      icon: <LayoutDashboard className="w-5 h-5" />,
    },
    {
      title: 'Leads',
      onClick: () => navigate('/admin?tab=leads'),
      icon: <Users className="w-5 h-5" />,
    },
    {
      title: 'Funcionários',
      onClick: () => navigate('/admin?tab=funcionarios'),
      icon: <UserPlus className="w-5 h-5" />,
    },
    {
      title: 'Relatórios',
      onClick: () => navigate('/admin?tab=relatorios'),
      icon: <FileText className="w-5 h-5" />,
    },
  ];

  const funcionarioNavItems: NavItem[] = [
    {
      title: 'Meus Leads',
      onClick: () => navigate('/funcionarios'),
      icon: <Users className="w-5 h-5" />,
    },
    {
      title: 'Novos Leads',
      onClick: () => navigate('/funcionarios?tab=novos-leads'),
      icon: <UserPlus className="w-5 h-5" />,
    },
    {
      title: 'Performance',
      onClick: () => navigate('/funcionarios?tab=performance'),
      icon: <TrendingUp className="w-5 h-5" />,
    },
  ];

  const navItems = isAdmin ? adminNavItems : funcionarioNavItems;

  const toggleItem = (title: string) => {
    setOpenItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const renderNavItem = (item: NavItem, depth: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = openItems.includes(item.title);
    
    // Verificar se é a rota ativa
    let isActive = false;
    if (item.onClick) {
      // Para admin dashboard
      if (location.pathname === '/admin') {
        const searchParams = new URLSearchParams(location.search);
        const tab = searchParams.get('tab');
        
        if (item.title === 'Visão Geral' && !tab) {
          isActive = true;
        } else if (item.title === 'Leads' && tab === 'leads') {
          isActive = true;
        } else if (item.title === 'Funcionários' && tab === 'funcionarios') {
          isActive = true;
        } else if (item.title === 'Relatórios' && tab === 'relatorios') {
          isActive = true;
        }
      }
      
      // Para funcionario dashboard
      if (location.pathname === '/funcionarios') {
        const searchParams = new URLSearchParams(location.search);
        const tab = searchParams.get('tab');
        
        if (item.title === 'Meus Leads' && !tab) {
          isActive = true;
        } else if (item.title === 'Novos Leads' && tab === 'novos-leads') {
          isActive = true;
        } else if (item.title === 'Performance' && tab === 'performance') {
          isActive = true;
        }
      }
    }

    if (hasChildren) {
      return (
        <Collapsible
          key={item.title}
          open={isOpen}
          onOpenChange={() => toggleItem(item.title)}
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-between hover:bg-gray-800 hover:text-white",
                depth > 0 && "pl-8"
              )}
            >
              <span className="flex items-center gap-3">
                {item.icon}
                <span>{item.title}</span>
              </span>
              <ChevronDown className={cn(
                "h-4 w-4 transition-transform",
                isOpen && "rotate-180"
              )} />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1">
            {item.children?.map(child => renderNavItem(child, depth + 1))}
          </CollapsibleContent>
        </Collapsible>
      );
    }

    return (
      <Button
        key={item.title}
        variant="ghost"
        onClick={item.onClick}
        className={cn(
          "w-full justify-start gap-3 hover:bg-gray-800 hover:text-white",
          depth > 0 && "pl-8",
          isActive && "bg-accent text-primary"
        )}
      >
        {item.icon}
        <span>{item.title}</span>
        {item.badge && (
          <span className="ml-auto text-xs bg-accent text-primary px-2 py-1 rounded">
            {item.badge}
          </span>
        )}
      </Button>
    );
  };

  return (
    <div className="flex h-full flex-col bg-primary text-white">
      {/* Logo */}
      <div className="p-6 border-b border-gray-800">
        <img 
          src={logoHeroDark} 
          alt="ASSISMAX Atacarejo" 
          className="h-12 w-auto brightness-0 invert"
        />
      </div>

      {/* User Info */}
      <div className="p-6 border-b border-gray-800">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-accent flex items-center justify-center">
            <span className="text-primary font-semibold">
              {funcionario?.nome?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <div className="flex-1">
            <p className="font-medium text-sm">{funcionario?.nome || 'Usuário'}</p>
            <p className="text-xs text-gray-400">
              {isAdmin ? 'Administrador' : 'Funcionário'}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {navItems.map(item => renderNavItem(item))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-800">
        <Button
          variant="ghost"
          className="w-full justify-start gap-3 hover:bg-gray-800 hover:text-white"
          onClick={() => navigate('/')}
        >
          <Settings className="w-5 h-5" />
          <span>Voltar ao Site</span>
        </Button>
        <Button
          variant="ghost"
          className="w-full justify-start gap-3 hover:bg-gray-800 hover:text-white text-red-400 hover:text-red-300"
          onClick={signOut}
        >
          <LogOut className="w-5 h-5" />
          <span>Sair</span>
        </Button>
      </div>
    </div>
  );
}