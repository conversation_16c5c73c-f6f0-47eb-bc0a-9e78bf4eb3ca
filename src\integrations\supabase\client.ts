// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rsydniuoipecgsocsuim.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzeWRuaXVvaXBlY2dzb2NzdWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwNDAxNzgsImV4cCI6MjA2ODYxNjE3OH0.DWMdClD8WzXL-B67HRIOfVVmz39H81gbIKzpMBnPTAk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});