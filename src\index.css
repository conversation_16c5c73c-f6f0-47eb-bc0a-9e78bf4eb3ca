@tailwind base;
@tailwind components;
@tailwind utilities;

/* AssisMax Atacarejo Design System */

@layer base {
  :root {
    /* Core Brand Colors - AssisMax Identity */
    --background: 0 0% 100%;
    --foreground: 0 0% 8%;

    /* Card System */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 8%;

    /* Popover System */
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 8%;

    /* Primary Brand - AssisMax Black */
    --primary: 0 0% 8%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 0 0% 15%;
    --primary-light: 0 0% 98%;

    /* Secondary Brand - Neutral Gray */
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    --secondary-hover: 0 0% 30%;
    --secondary-light: 0 0% 96%;

    /* Accent - AssisMax Yellow */
    --accent: 51 100% 59%;
    --accent-foreground: 0 0% 8%;
    --accent-hover: 51 100% 54%;
    --accent-light: 51 100% 96%;

    /* Neutral System */
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --muted-hover: 0 0% 92%;

    /* Status Colors */
    --success: 120 100% 25%;
    --success-foreground: 0 0% 100%;
    --warning: 51 100% 59%;
    --warning-foreground: 0 0% 8%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border System */
    --border: 0 0% 90%;
    --input: 0 0% 88%;
    --ring: 51 100% 59%;

    /* Gradients - AssisMax Style */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-hover)) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--secondary-hover)) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent-hover)) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);

    /* Shadows - Yellow Accent */
    --shadow-soft: 0 2px 8px hsl(0 0% 8% / 0.1);
    --shadow-medium: 0 4px 16px hsl(0 0% 8% / 0.15);
    --shadow-strong: 0 8px 32px hsl(0 0% 8% / 0.2);
    --shadow-glow: 0 0 24px hsl(var(--accent) / 0.4);

    /* Animations */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Animações personalizadas para dashboard */
  @keyframes fade-in-up {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slide-in-right {
    0% {
      opacity: 0;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.4s ease-out;
  }

  .dark {
    /* Core Colors - AssisMax Dark Mode */
    --background: 0 0% 8%;
    --foreground: 0 0% 95%;

    /* Card System */
    --card: 0 0% 10%;
    --card-foreground: 0 0% 95%;

    /* Popover System */
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 95%;

    /* Primary Brand - Dark Mode */
    --primary: 0 0% 95%;
    --primary-foreground: 0 0% 8%;
    --primary-hover: 0 0% 85%;
    --primary-light: 0 0% 15%;

    /* Secondary Brand - Dark Mode */
    --secondary: 0 0% 70%;
    --secondary-foreground: 0 0% 8%;
    --secondary-hover: 0 0% 80%;
    --secondary-light: 0 0% 12%;

    /* Accent - AssisMax Yellow Dark Mode */
    --accent: 51 100% 64%;
    --accent-foreground: 0 0% 8%;
    --accent-hover: 51 100% 69%;
    --accent-light: 51 100% 12%;

    /* Neutral System */
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 60%;
    --muted-hover: 0 0% 20%;

    /* Status Colors */
    --success: 120 100% 30%;
    --success-foreground: 0 0% 8%;
    --warning: 51 100% 64%;
    --warning-foreground: 0 0% 8%;
    --destructive: 0 84% 65%;
    --destructive-foreground: 0 0% 8%;

    /* Border System */
    --border: 0 0% 20%;
    --input: 0 0% 18%;
    --ring: 51 100% 64%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Hero Buttons */
  .btn-hero {
    @apply bg-gradient-primary text-primary-foreground px-8 py-4 rounded-lg font-semibold text-lg shadow-medium hover:shadow-strong transition-all duration-300 hover:scale-105;
  }

  .btn-hero-secondary {
    @apply bg-gradient-secondary text-secondary-foreground px-8 py-4 rounded-lg font-semibold text-lg shadow-medium hover:shadow-strong transition-all duration-300 hover:scale-105;
  }

  .btn-accent {
    @apply bg-gradient-accent text-accent-foreground px-6 py-3 rounded-lg font-medium shadow-soft hover:shadow-glow transition-all duration-300 hover:scale-105;
  }

  /* Cards */
  .card-floating {
    @apply bg-card border border-border rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 hover:-translate-y-1;
  }

  .card-product {
    @apply bg-gradient-card border border-border rounded-xl p-6 shadow-medium hover:shadow-strong transition-all duration-300 hover:scale-105;
  }

  /* Text Effects */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary-hover bg-clip-text text-transparent;
  }

  .text-gradient-hero {
    @apply bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent;
  }

  /* Interactive Elements */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-2 hover:shadow-strong;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-glow;
  }

  /* Layout */
  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }

  .container-responsive {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Chatbot Animations */
  @keyframes bounce {
    0%, 80%, 100% {
      transform: scale(1);
      opacity: 0.5;
    }
    40% {
      transform: scale(1.3);
      opacity: 1;
    }
  }

  .animate-bounce {
    animation: bounce 1.4s infinite ease-in-out;
  }

  /* Fade in animations for messages */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
}