import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Create regular client for RLS checks
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            Authorization: req.headers.get('Authorization') ?? ''
          }
        }
      }
    )

    const { nome, email, telefone, nivel_acesso, senha, empresa_slug } = await req.json()

    console.log('Iniciando cadastro de funcionário:', { email, nivel_acesso, empresa_slug })

    // 1. Verificar se o usuário atual é admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      throw new Error('Usuário não autenticado')
    }

    console.log('Usuário autenticado:', user.email)

    // 2. Verificar se o usuário atual é admin da empresa
    const { data: adminUser, error: adminError } = await supabase
      .from('funcionarios')
      .select('nivel_acesso, empresa_id')
      .eq('email', user.email)
      .eq('ativo', true)
      .single()

    if (adminError || !adminUser || adminUser.nivel_acesso !== 'admin') {
      throw new Error('Apenas administradores podem criar funcionários')
    }

    console.log('Admin verificado:', adminUser)

    // 3. Buscar empresa
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('id')
      .eq('slug', empresa_slug || 'assismax')
      .eq('ativo', true)
      .single()

    if (empresaError || !empresa) {
      throw new Error('Empresa não encontrada')
    }

    console.log('Empresa encontrada:', empresa.id)

    // 4. Verificar se funcionário já existe
    const { data: existingFuncionario } = await supabaseAdmin
      .from('funcionarios')
      .select('id')
      .eq('email', email)
      .single()

    if (existingFuncionario) {
      throw new Error('Funcionário com este email já existe')
    }

    // 5. Criar usuário no Auth usando admin client
    console.log('Criando usuário no Auth...')
    const { data: authData, error: createAuthError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: senha,
      email_confirm: true,
      user_metadata: {
        nome,
        nivel_acesso
      }
    })

    if (createAuthError) {
      console.error('Erro ao criar usuário no Auth:', createAuthError)
      throw createAuthError
    }

    console.log('Usuário criado no Auth:', authData.user.id)

    // 6. Criar funcionário na tabela usando admin client
    console.log('Criando funcionário na tabela...')
    const { data: funcionario, error: funcionarioError } = await supabaseAdmin
      .from('funcionarios')
      .insert({
        nome,
        email,
        telefone: telefone || null,
        nivel_acesso,
        empresa_id: empresa.id,
        ativo: true
      })
      .select()
      .single()

    if (funcionarioError) {
      console.error('Erro ao criar funcionário:', funcionarioError)
      
      // Tentar limpar usuário do Auth se funcionário falhou
      try {
        await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
        console.log('Usuário removido do Auth após falha')
      } catch (cleanupError) {
        console.error('Erro ao limpar usuário do Auth:', cleanupError)
      }
      
      throw funcionarioError
    }

    console.log('Funcionário criado com sucesso:', funcionario.id)

    return new Response(
      JSON.stringify({
        success: true,
        funcionario,
        message: 'Funcionário criado com sucesso'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Erro na Edge Function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Erro interno do servidor'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
