import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  TrendingUp, 
  MessageSquare, 
  Target, 
  RefreshCw, 
  ArrowLeft,
  Calendar,
  BarChart3,
  Download,
  LogOut,
  UserPlus,
  FileText,
  Activity,
  Package
} from 'lucide-react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import { DashboardLayout } from '@/components/dashboard/layout/DashboardLayout';
import { MetricCard } from '@/components/dashboard/cards/MetricCard';
import { LeadsTable } from '@/components/dashboard/LeadsTable';
import { LeadsFilters } from '@/components/dashboard/LeadsFilters';
import { FuncionariosManager } from '@/components/admin/FuncionariosManager';
import { ProdutosManager } from '@/components/admin/ProdutosManager';
import { useDashboard, useLeads } from '@/hooks/useDashboard';
import { useSimpleDashboard } from '@/hooks/useSimpleDashboard';
import { useAuth } from '@/hooks/useAuth';
import { DonutChart } from '@/components/dashboard/charts/DonutChart';
import { BarChart } from '@/components/dashboard/charts/BarChart';
import { LineChart } from '@/components/dashboard/charts/LineChart';
import AdminChatbotModal from '@/components/admin/AdminChatbotModal';

export default function AdminDashboard() {
  const { metrics, isLoadingMetrics, metricsError } = useDashboard();
  const { leads, isLoadingLeads, leadsError, filters, updateFilters, clearFilters, refetchLeads } = useLeads();
  const { leads: simpleLeads, isLoading: simpleLoading, error: simpleError } = useSimpleDashboard();
  const { funcionario, signOut } = useAuth();
  const navigate = useNavigate();
  const [chatbotOpen, setChatbotOpen] = useState(false);
  
  // Ler tab da URL
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const activeTab = searchParams.get('tab') || 'overview';

  const handleTabChange = (value: string) => {
    navigate(`/admin?tab=${value}`);
  };

  const handleRefresh = () => {
    refetchLeads();
    window.location.reload(); // Para atualizar métricas também
  };

  // Debug: log dos dados recebidos
  console.log('Simple leads:', simpleLeads);
  console.log('Simple loading:', simpleLoading);
  console.log('Simple error:', simpleError);

  // Preparar dados para gráficos usando useMemo
  const weeklyLeadsData = React.useMemo(() => {
    if (!metrics?.metricasPorDia || metrics.metricasPorDia.length === 0) {
      return [];
    }

    // Obter últimos 7 dias
    const hoje = new Date();
    const diasSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    
    return Array.from({ length: 7 }, (_, i) => {
      const data = new Date(hoje);
      data.setDate(hoje.getDate() - (6 - i));
      const dataStr = data.toISOString().split('T')[0];
      const diaSemana = diasSemana[data.getDay()];
      
      const dadosDia = metrics.metricasPorDia.find(m => m.data === dataStr);
      const leads = dadosDia?.leads || 0;
      
      // Estimar conversões baseado na taxa de conversão geral
      const conversoes = Math.round(leads * (metrics.taxaConversao / 100));
      
      return {
        name: diaSemana,
        leads,
        conversoes
      };
    });
  }, [metrics?.metricasPorDia, metrics?.taxaConversao]);

  const conversionTrendData = React.useMemo(() => {
    if (!metrics?.metricasPorDia || metrics.metricasPorDia.length === 0) {
      return [];
    }

    // Agrupar dados por semana dos últimos 30 dias
    const dataAtual = new Date();
    const semanas = [];
    
    for (let i = 0; i < 6; i++) {
      const inicioSemana = new Date(dataAtual);
      inicioSemana.setDate(dataAtual.getDate() - (i * 7) - 6);
      const fimSemana = new Date(dataAtual);
      fimSemana.setDate(dataAtual.getDate() - (i * 7));
      
      const dadosSemana = metrics.metricasPorDia.filter(m => {
        const data = new Date(m.data);
        return data >= inicioSemana && data <= fimSemana;
      });
      
      const totalLeads = dadosSemana.reduce((sum, d) => sum + d.leads, 0);
      const conversoes = Math.round(totalLeads * (metrics.taxaConversao / 100));
      const taxa = totalLeads > 0 ? Math.round((conversoes / totalLeads) * 100) : 0;
      
      semanas.unshift({
        name: `S${i + 1}`,
        taxa: taxa
      });
    }
    
    return semanas.slice(0, 6);
  }, [metrics?.metricasPorDia, metrics?.taxaConversao]);

  if (metricsError || leadsError || simpleError) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Erro ao carregar dados</CardTitle>
            <CardDescription>
              Não foi possível conectar ao banco de dados. Verifique a conexão.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()} className="w-full">
              Tentar novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Preparar dados para gráficos
  const leadStatusChartData = metrics?.leadsPorStatus?.map(item => ({
    name: item.status.replace('_', ' ').charAt(0).toUpperCase() + item.status.replace('_', ' ').slice(1),
    value: item.total,
    color: item.status === 'novo' ? '#3B82F6' : 
           item.status === 'em_atendimento' ? '#FFD831' : 
           item.status === 'qualificado' ? '#8B5CF6' :
           item.status === 'convertido' ? '#10B981' : '#EF4444'
  })) || [];

  return (
    <>
    <DashboardLayout onChatbotToggle={() => setChatbotOpen(true)}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Dashboard Administrativo</h1>
            <p className="text-muted-foreground mt-1">
              Bem-vindo {funcionario?.nome}, acompanhe as métricas do ASSISMAX
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="gap-1">
              <Calendar className="w-3 h-3" />
              {new Date().toLocaleDateString('pt-BR')}
            </Badge>
            <Button onClick={handleRefresh} size="sm" variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Atualizar
            </Button>
          </div>
        </div>
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-white shadow-soft">
            <TabsTrigger value="overview" className="data-[state=active]:bg-accent data-[state=active]:text-primary">
              <Activity className="w-4 h-4 mr-2" />
              Visão Geral
            </TabsTrigger>
            <TabsTrigger value="leads" className="data-[state=active]:bg-accent data-[state=active]:text-primary">
              <Users className="w-4 h-4 mr-2" />
              Leads
            </TabsTrigger>
            <TabsTrigger value="funcionarios" className="data-[state=active]:bg-accent data-[state=active]:text-primary">
              <UserPlus className="w-4 h-4 mr-2" />
              Funcionários
            </TabsTrigger>
            <TabsTrigger value="produtos" className="data-[state=active]:bg-accent data-[state=active]:text-primary">
              <Package className="w-4 h-4 mr-2" />
              Produtos
            </TabsTrigger>
            <TabsTrigger value="relatorios" className="data-[state=active]:bg-accent data-[state=active]:text-primary">
              <FileText className="w-4 h-4 mr-2" />
              Relatórios
            </TabsTrigger>
          </TabsList>

          {/* VISÃO GERAL */}
          <TabsContent value="overview" className="space-y-6 animate-fade-in-up">
            {/* KPIs Principais */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <MetricCard
                title="Leads Hoje"
                value={isLoadingMetrics ? '...' : metrics?.leadsHoje || 0}
                description="Captados nas últimas 24h"
                icon={<Users />}
                color="info"
                trend={{ value: 15, type: 'up' }}
              />
              <MetricCard
                title="Leads esta Semana"
                value={isLoadingMetrics ? '...' : metrics?.leadsSemana || 0}
                description="Últimos 7 dias"
                icon={<TrendingUp />}
                color="success"
                trend={{ value: 8, type: 'up' }}
              />
              <MetricCard
                title="Taxa de Conversão"
                value={isLoadingMetrics ? '...' : `${metrics?.taxaConversao || 0}%`}
                description="Leads → Clientes"
                icon={<Target />}
                color="warning"
                trend={{ value: 3, type: 'down' }}
              />
              <MetricCard
                title="Total de Leads"
                value={isLoadingMetrics ? '...' : metrics?.leadsTotal || 0}
                description="Desde o início"
                icon={<MessageSquare />}
                color="default"
              />
            </div>

            {/* Gráficos Principais */}
            <div className="grid gap-6 lg:grid-cols-2">
              <DonutChart
                data={leadStatusChartData}
                title="Distribuição de Leads por Status"
                description="Visão geral do funil de vendas"
              />
              <BarChart
                data={weeklyLeadsData}
                bars={[
                  { dataKey: 'leads', name: 'Leads Captados', color: '#FFD831' },
                  { dataKey: 'conversoes', name: 'Conversões', color: '#10B981' }
                ]}
                title="Atividade Semanal"
                description="Leads captados e convertidos por dia"
              />
            </div>

            {/* Gráfico de Tendência */}
            <LineChart
              data={conversionTrendData}
              lines={[
                { dataKey: 'taxa', name: 'Taxa de Conversão (%)', color: '#8B5CF6' }
              ]}
              title="Tendência de Conversão"
              description="Evolução da taxa de conversão nos últimos 6 meses"
              showArea
            />

            {/* Leads Recentes */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Leads Recentes</CardTitle>
                <CardDescription>Últimos 10 leads capturados</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingLeads ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <div className="h-4 bg-muted rounded animate-pulse flex-1" />
                        <div className="h-4 bg-muted rounded animate-pulse w-20" />
                        <div className="h-4 bg-muted rounded animate-pulse w-16" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <LeadsTable leads={simpleLeads?.slice(0, 10) || []} onRefresh={refetchLeads} />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* GESTÃO DE LEADS */}
          <TabsContent value="leads" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">Gestão de Leads</h2>
                <p className="text-muted-foreground">
                  {simpleLoading ? 'Carregando...' : `${simpleLeads?.length || 0} leads encontrados`}
                </p>
              </div>
              <Button onClick={refetchLeads} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Atualizar Lista
              </Button>
            </div>

            <LeadsFilters
              filters={filters}
              onFiltersChange={updateFilters}
              onClearFilters={clearFilters}
            />

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Lista de Leads</CardTitle>
                <CardDescription>
                  Gerencie todos os leads capturados pelo sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                {simpleLoading ? (
                  <div className="space-y-3">
                    {[...Array(10)].map((_, i) => (
                      <div key={i} className="h-12 bg-muted rounded animate-pulse" />
                    ))}
                  </div>
                ) : (
                  <LeadsTable leads={simpleLeads || []} onRefresh={refetchLeads} />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* RELATÓRIOS */}
          <TabsContent value="relatorios" className="space-y-6 animate-fade-in-up">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">Relatórios e Análises</h2>
                <p className="text-muted-foreground">
                  Insights detalhados para tomada de decisão
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Calendar className="w-4 h-4 mr-2" />
                  Período
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Exportar PDF
                </Button>
              </div>
            </div>

            {/* Resumo do Período */}
            <div className="grid gap-4 md:grid-cols-4">
              <MetricCard
                title="Ticket Médio"
                value={isLoadingMetrics ? '...' : 'R$ 287,50'}
                description="Valor médio por cliente"
                icon={<BarChart3 />}
                color="info"
                trend={{ value: 8.5, type: 'up' }}
              />
              <MetricCard
                title="Produtos Mais Vendidos"
                value={isLoadingMetrics ? '...' : '5'}
                description="Arroz, Feijão, Óleo..."
                icon={<Package />}
                color="warning"
              />
              <MetricCard
                title="Taxa de Retorno"
                value={isLoadingMetrics ? '...' : '68%'}
                description="Clientes que voltaram"
                icon={<RefreshCw />}
                color="success"
                trend={{ value: 15, type: 'up' }}
              />
              <MetricCard
                title="ROI Marketing"
                value={isLoadingMetrics ? '...' : '4.2x'}
                description="Retorno sobre investimento"
                icon={<TrendingUp />}
                color="default"
                trend={{ value: 22, type: 'up' }}
              />
            </div>

            {/* Análises Detalhadas */}
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Performance de Vendas */}
              <Card className="shadow-soft">
                <CardHeader>
                  <CardTitle className="text-lg">Performance de Vendas</CardTitle>
                  <CardDescription>
                    Análise comparativa mensal e tendências
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div>
                        <p className="font-medium">Vendas Este Mês</p>
                        <p className="text-2xl font-bold">R$ 487.350</p>
                      </div>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        +18% vs mês anterior
                      </Badge>
                    </div>
                    <Separator />
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Meta Mensal</span>
                        <span className="font-medium">R$ 500.000</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Atingido</span>
                        <span className="font-medium">97.5%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Dias Restantes</span>
                        <span className="font-medium">8 dias</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Top Produtos */}
              <Card className="shadow-soft">
                <CardHeader>
                  <CardTitle className="text-lg">Produtos Mais Vendidos</CardTitle>
                  <CardDescription>
                    Ranking de produtos por volume de vendas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { nome: 'Arroz Tipo 1 - 5kg', vendas: '1.234 un', faturamento: 'R$ 24.680' },
                      { nome: 'Feijão Carioca - 1kg', vendas: '987 un', faturamento: 'R$ 8.883' },
                      { nome: 'Óleo de Soja - 900ml', vendas: '876 un', faturamento: 'R$ 5.256' },
                      { nome: 'Açúcar Cristal - 5kg', vendas: '654 un', faturamento: 'R$ 11.772' },
                      { nome: 'Café Tradicional - 500g', vendas: '543 un', faturamento: 'R$ 7.062' }
                    ].map((produto, index) => (
                      <div key={index} className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-lg transition-colors">
                        <div className="flex items-center gap-3">
                          <span className="text-lg font-bold text-muted-foreground">#{index + 1}</span>
                          <div>
                            <p className="font-medium text-sm">{produto.nome}</p>
                            <p className="text-xs text-muted-foreground">{produto.vendas}</p>
                          </div>
                        </div>
                        <span className="font-semibold text-sm">{produto.faturamento}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Relatórios Disponíveis */}
            <Card className="shadow-soft">
              <CardHeader>
                <CardTitle>Relatórios Personalizados</CardTitle>
                <CardDescription>
                  Gere relatórios detalhados para análise aprofundada
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <Button variant="outline" className="h-auto p-4 justify-start group hover:border-primary">
                    <div className="text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="w-5 h-5 text-primary" />
                        <p className="font-semibold">Análise de Vendas</p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Faturamento, ticket médio e sazonalidade
                      </p>
                    </div>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 justify-start group hover:border-primary">
                    <div className="text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5 text-primary" />
                        <p className="font-semibold">Comportamento de Clientes</p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Frequência de compra e preferências
                      </p>
                    </div>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 justify-start group hover:border-primary">
                    <div className="text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <Package className="w-5 h-5 text-primary" />
                        <p className="font-semibold">Estoque e Giro</p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Produtos com maior rotatividade
                      </p>
                    </div>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 justify-start group hover:border-primary">
                    <div className="text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <Target className="w-5 h-5 text-primary" />
                        <p className="font-semibold">Eficiência de Conversão</p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Funil de vendas e pontos de melhoria
                      </p>
                    </div>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 justify-start group hover:border-primary">
                    <div className="text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-5 h-5 text-primary" />
                        <p className="font-semibold">Projeções e Tendências</p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Previsões baseadas em histórico
                      </p>
                    </div>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 justify-start group hover:border-primary">
                    <div className="text-left space-y-1">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-5 h-5 text-primary" />
                        <p className="font-semibold">Atendimento WhatsApp</p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Tempo de resposta e satisfação
                      </p>
                    </div>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* FUNCIONÁRIOS */}
          <TabsContent value="funcionarios" className="space-y-6 animate-fade-in-up">
            <FuncionariosManager />
          </TabsContent>

          {/* PRODUTOS */}
          <TabsContent value="produtos" className="space-y-6 animate-fade-in-up">
            <ProdutosManager />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>

    {/* Chatbot Modal */}
    <AdminChatbotModal 
      open={chatbotOpen} 
      onOpenChange={setChatbotOpen}
    />
    </>
  );
}