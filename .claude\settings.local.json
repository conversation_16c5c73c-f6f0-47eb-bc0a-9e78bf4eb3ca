{"permissions": {"allow": ["<PERSON><PERSON>(claude mcp:*)", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__supabase__deploy_edge_function", "mcp__supabase__execute_sql", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(npx vite:*)", "Bash(npx tsc:*)", "Bash(npm run lint)", "<PERSON><PERSON>(touch:*)", "Bash(npm:*)", "Bash(bun install:*)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(pkill:*)", "mcp__supabase__list_extensions", "mcp__supabase__apply_migration", "mcp__supabase__list_edge_functions", "Bash(npx:*)", "Bash(supabase functions deploy:*)", "mcp__supabase__get_logs", "<PERSON><PERSON>(timeout 5s npm run dev)", "Bash(timeout 3s npm run dev)", "<PERSON><PERSON>(start:*)", "<PERSON>sh(xdg-open:*)", "Bash(supabase functions:*)", "<PERSON><PERSON>(docker:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__supabase__get_anon_key"], "deny": []}}