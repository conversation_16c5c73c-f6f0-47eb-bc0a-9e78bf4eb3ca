import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { UserPlus, Loader2 } from 'lucide-react';

const funcionarioSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  telefone: z.string().min(10, 'Telefone deve ter pelo menos 10 dígitos'),
  nivel_acesso: z.enum(['admin', 'funcionario'], {
    required_error: 'Selecione um nível de acesso',
  }),
});

type FuncionarioFormData = z.infer<typeof funcionarioSchema>;

interface CreateFuncionarioModalProps {
  onSuccess?: () => void;
}

export function CreateFuncionarioModal({ onSuccess }: CreateFuncionarioModalProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<FuncionarioFormData>({
    resolver: zodResolver(funcionarioSchema),
    defaultValues: {
      nome: '',
      email: '',
      telefone: '',
      nivel_acesso: 'funcionario',
    },
  });

  const onSubmit = async (data: FuncionarioFormData) => {
    setIsLoading(true);

    try {
      // Buscar empresa ASSISMAX
      const { data: empresa, error: empresaError } = await supabase
        .from('empresas')
        .select('id')
        .eq('slug', 'assismax')
        .eq('ativo', true)
        .single();

      if (empresaError || !empresa) {
        throw new Error('Empresa ASSISMAX não encontrada');
      }

      // Verificar se funcionário já existe
      const { data: existingFuncionario } = await supabase
        .from('funcionarios')
        .select('id')
        .eq('email', data.email)
        .single();

      if (existingFuncionario) {
        throw new Error('Funcionário com este email já existe');
      }

      // Criar funcionário
      const { data: newFuncionario, error } = await supabase
        .from('funcionarios')
        .insert({
          nome: data.nome,
          email: data.email,
          telefone: data.telefone,
          nivel_acesso: data.nivel_acesso,
          empresa_id: empresa.id,
          ativo: true,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      toast({
        title: 'Funcionário criado com sucesso! ✅',
        description: `${data.nome} pode agora fazer login no sistema.`,
      });

      // Resetar form e fechar modal
      form.reset();
      setOpen(false);
      onSuccess?.();

    } catch (error) {
      console.error('Erro ao criar funcionário:', error);
      const message = error instanceof Error ? error.message : 'Erro ao criar funcionário';
      
      toast({
        title: 'Erro ao criar funcionário',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="w-4 h-4 mr-2" />
          Criar Funcionário
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Criar Novo Funcionário</DialogTitle>
          <DialogDescription>
            Adicione um novo funcionário ao sistema. Ele poderá fazer login com o email fornecido.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="nome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome Completo</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: João Silva" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input 
                      type="email" 
                      placeholder="<EMAIL>" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="telefone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telefone</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="(61) 99999-9999" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nivel_acesso"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nível de Acesso</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o nível" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="funcionario">Funcionário</SelectItem>
                      <SelectItem value="admin">Administrador</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Criar Funcionário
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 