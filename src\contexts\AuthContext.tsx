import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { User } from '@supabase/supabase-js';

interface Funcionario {
  id: string;
  nome: string;
  email: string;
  nivel_acesso: 'admin' | 'funcionario';
  empresa_id: string;
  ativo: boolean;
}

interface AuthContextType {
  user: User | null;
  funcionario: Funcionario | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isValidUser: boolean; // Novo: usuário está autenticado no Supabase Auth
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [funcionario, setFuncionario] = useState<Funcionario | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  const isAuthenticated = !!user && !!funcionario;
  const isValidUser = !!user; // Usuário logado no Supabase Auth (independente do funcionário)
  const isAdmin = funcionario?.nivel_acesso === 'admin';

  // Buscar dados do funcionário baseado no email do usuário autenticado
  const fetchFuncionario = async (userEmail: string) => {
    try {
      console.log('Buscando funcionário para email:', userEmail);
      const { data, error } = await supabase
        .from('funcionarios')
        .select('*')
        .eq('email', userEmail)
        .eq('ativo', true)
        .single();

      console.log('Resultado da busca:', { data, error });

      if (error) {
        console.error('Erro Supabase na busca:', error);
        if (error.code === 'PGRST116') {
          throw new Error(`Funcionário ${userEmail} não existe na tabela funcionarios. Contate o administrador.`);
        }
        throw new Error(`Erro ao buscar funcionário: ${error.message}`);
      }

      if (!data) {
        throw new Error(`Funcionário ${userEmail} não encontrado ou inativo. Contate o administrador.`);
      }

      console.log('Funcionário encontrado:', data);
      setFuncionario(data as Funcionario);
      return data;
    } catch (error) {
      console.error('Erro ao buscar funcionário:', error);
      throw error;
    }
  };

  // Verificar sessão atual (função para uso manual)
  const checkAuth = async () => {
    try {
      setIsLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        setUser(session.user);
        try {
          await fetchFuncionario(session.user.email!);
        } catch (error) {
          console.error('Erro ao buscar funcionário na verificação manual:', error);
          setFuncionario(null);
        }
      } else {
        setUser(null);
        setFuncionario(null);
      }
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      setUser(null);
      setFuncionario(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Login
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);

      // Primeiro, fazer login no Supabase Auth
      console.log('Tentando fazer login para:', email);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Erro no Supabase Auth:', error);
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Email ou senha incorretos');
        }
        throw error;
      }

      if (data.user) {
        console.log('Login no Auth bem-sucedido:', data.user.id);
        setUser(data.user);

        // Agora buscar dados do funcionário (já autenticado)
        try {
          const funcionarioData = await fetchFuncionario(data.user.email!);

          toast({
            title: 'Login realizado com sucesso!',
            description: `Bem-vindo, ${funcionarioData.nome}`,
          });

          // Redirecionar baseado no nível de acesso
          if (funcionarioData.nivel_acesso === 'admin') {
            navigate('/admin');
          } else {
            navigate('/funcionarios');
          }
        } catch (funcionarioError) {
          console.error('Funcionário não encontrado após login:', funcionarioError);
          // Se funcionário não existir, fazer logout e mostrar erro
          await supabase.auth.signOut();
          setUser(null);
          setFuncionario(null);
          throw new Error(`Sua conta ${data.user.email} não está registrada como funcionário. Vá para /setup para criar um funcionário ou contate o administrador.`);
        }
      }
    } catch (error) {
      console.error('Erro no login:', error);
      const errorMessage = error instanceof Error ? error.message : 'Verifique suas credenciais';
      toast({
        title: 'Erro ao fazer login',
        description: errorMessage,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout
  const signOut = async () => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase.auth.signOut();
      
      if (error) throw error;

      setUser(null);
      setFuncionario(null);
      
      toast({
        title: 'Logout realizado',
        description: 'Você foi desconectado com sucesso',
      });
      
      navigate('/');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro ao fazer logout';
      toast({
        title: 'Erro ao fazer logout',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Monitorar mudanças de autenticação
  useEffect(() => {
    // Função para verificar auth uma única vez
    const initAuth = async () => {
      try {
        console.log('🔍 Iniciando verificação de autenticação...');
        setIsLoading(true);
        
        const { data: { session } } = await supabase.auth.getSession();
        console.log('📋 Sessão obtida:', session ? 'Existe' : 'Não existe');
        
        if (session?.user) {
          console.log('👤 Usuário encontrado:', session.user.email);
          setUser(session.user);
          try {
            console.log('🔍 Buscando funcionário...');
            await fetchFuncionario(session.user.email!);
            console.log('✅ Funcionário encontrado com sucesso');
          } catch (error) {
            console.error('❌ Erro ao buscar funcionário na verificação inicial:', error);
            // Se há erro na busca do funcionário, manter o user mas sem funcionário
            setFuncionario(null);
          }
        } else {
          console.log('❌ Nenhum usuário logado');
          setUser(null);
          setFuncionario(null);
        }
      } catch (error) {
        console.error('💥 Erro ao verificar autenticação:', error);
        setUser(null);
        setFuncionario(null);
      } finally {
        console.log('🏁 Finalizando verificação de autenticação, setIsLoading(false)');
        setIsLoading(false);
      }
    };

    initAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);

      if (event === 'SIGNED_IN' && session?.user) {
        setUser(session.user);
        try {
          await fetchFuncionario(session.user.email!);
        } catch (error) {
          console.log('Funcionário não encontrado para:', session.user.email);
          // IMPORTANTE: Não fazer logout automático aqui
          // Isso pode ser um usuário sendo criado por um admin
          // Apenas limpar o funcionário, mas manter a sessão
          setFuncionario(null);
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setFuncionario(null);
      } else if (event === 'TOKEN_REFRESHED' && session?.user) {
        setUser(session.user);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        funcionario,
        isLoading,
        isAuthenticated,
        isValidUser,
        isAdmin,
        signIn,
        signOut,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
}